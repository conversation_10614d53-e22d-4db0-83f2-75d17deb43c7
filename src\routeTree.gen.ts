/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthRouteImport } from './routes/_auth'
import { Route as AuthIndexRouteImport } from './routes/_auth/index'
import { Route as AuthTopologyRouteImport } from './routes/_auth/topology'
import { Route as AuthLogsRouteImport } from './routes/_auth/logs'
import { Route as AuthIdpsRouteImport } from './routes/_auth/idps'
import { Route as AuthDevicesRouteImport } from './routes/_auth/devices'
import { Route as AuthDashboardRouteImport } from './routes/_auth/dashboard'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthRoute = AuthRouteImport.update({
  id: '/_auth',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthIndexRoute = AuthIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthRoute,
} as any)
const AuthTopologyRoute = AuthTopologyRouteImport.update({
  id: '/topology',
  path: '/topology',
  getParentRoute: () => AuthRoute,
} as any)
const AuthLogsRoute = AuthLogsRouteImport.update({
  id: '/logs',
  path: '/logs',
  getParentRoute: () => AuthRoute,
} as any)
const AuthIdpsRoute = AuthIdpsRouteImport.update({
  id: '/idps',
  path: '/idps',
  getParentRoute: () => AuthRoute,
} as any)
const AuthDevicesRoute = AuthDevicesRouteImport.update({
  id: '/devices',
  path: '/devices',
  getParentRoute: () => AuthRoute,
} as any)
const AuthDashboardRoute = AuthDashboardRouteImport.update({
  id: '/dashboard',
  path: '/dashboard',
  getParentRoute: () => AuthRoute,
} as any)

export interface FileRoutesByFullPath {
  '/login': typeof LoginRoute
  '/dashboard': typeof AuthDashboardRoute
  '/devices': typeof AuthDevicesRoute
  '/idps': typeof AuthIdpsRoute
  '/logs': typeof AuthLogsRoute
  '/topology': typeof AuthTopologyRoute
  '/': typeof AuthIndexRoute
}
export interface FileRoutesByTo {
  '/login': typeof LoginRoute
  '/dashboard': typeof AuthDashboardRoute
  '/devices': typeof AuthDevicesRoute
  '/idps': typeof AuthIdpsRoute
  '/logs': typeof AuthLogsRoute
  '/topology': typeof AuthTopologyRoute
  '/': typeof AuthIndexRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/_auth': typeof AuthRouteWithChildren
  '/login': typeof LoginRoute
  '/_auth/dashboard': typeof AuthDashboardRoute
  '/_auth/devices': typeof AuthDevicesRoute
  '/_auth/idps': typeof AuthIdpsRoute
  '/_auth/logs': typeof AuthLogsRoute
  '/_auth/topology': typeof AuthTopologyRoute
  '/_auth/': typeof AuthIndexRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/login'
    | '/dashboard'
    | '/devices'
    | '/idps'
    | '/logs'
    | '/topology'
    | '/'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/login'
    | '/dashboard'
    | '/devices'
    | '/idps'
    | '/logs'
    | '/topology'
    | '/'
  id:
    | '__root__'
    | '/_auth'
    | '/login'
    | '/_auth/dashboard'
    | '/_auth/devices'
    | '/_auth/idps'
    | '/_auth/logs'
    | '/_auth/topology'
    | '/_auth/'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  AuthRoute: typeof AuthRouteWithChildren
  LoginRoute: typeof LoginRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_auth': {
      id: '/_auth'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_auth/': {
      id: '/_auth/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof AuthIndexRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/topology': {
      id: '/_auth/topology'
      path: '/topology'
      fullPath: '/topology'
      preLoaderRoute: typeof AuthTopologyRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/logs': {
      id: '/_auth/logs'
      path: '/logs'
      fullPath: '/logs'
      preLoaderRoute: typeof AuthLogsRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/idps': {
      id: '/_auth/idps'
      path: '/idps'
      fullPath: '/idps'
      preLoaderRoute: typeof AuthIdpsRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/devices': {
      id: '/_auth/devices'
      path: '/devices'
      fullPath: '/devices'
      preLoaderRoute: typeof AuthDevicesRouteImport
      parentRoute: typeof AuthRoute
    }
    '/_auth/dashboard': {
      id: '/_auth/dashboard'
      path: '/dashboard'
      fullPath: '/dashboard'
      preLoaderRoute: typeof AuthDashboardRouteImport
      parentRoute: typeof AuthRoute
    }
  }
}

interface AuthRouteChildren {
  AuthDashboardRoute: typeof AuthDashboardRoute
  AuthDevicesRoute: typeof AuthDevicesRoute
  AuthIdpsRoute: typeof AuthIdpsRoute
  AuthLogsRoute: typeof AuthLogsRoute
  AuthTopologyRoute: typeof AuthTopologyRoute
  AuthIndexRoute: typeof AuthIndexRoute
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthDashboardRoute: AuthDashboardRoute,
  AuthDevicesRoute: AuthDevicesRoute,
  AuthIdpsRoute: AuthIdpsRoute,
  AuthLogsRoute: AuthLogsRoute,
  AuthTopologyRoute: AuthTopologyRoute,
  AuthIndexRoute: AuthIndexRoute,
}

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren)

const rootRouteChildren: RootRouteChildren = {
  AuthRoute: AuthRouteWithChildren,
  LoginRoute: LoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
