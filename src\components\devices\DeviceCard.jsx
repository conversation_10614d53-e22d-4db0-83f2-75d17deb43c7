import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er } from "../ui/card";

const InfoRow = ({ label, value }) => (
  <div className="flex justify-between text-sm">
    <p className="text-muted-foreground">{label}</p>
    <p className="font-medium text-foreground">{value}</p>
  </div>
);

const DeviceCard = ({ device }) => {
  return (
    <div className="w-full max-w-sm overflow-hidden border rounded-lg shadow-lg bg-card border-border transition-transform duration-300 ease-in-out hover:shadow-2xl hover:-translate-y-1">
      <div className="relative">
        <img
          className="object-cover w-full h-48"
          src={`https://picsum.photos/seed/${device.modelname}/400/300`}
          alt={device.modelname}
        />
        <div
          className={`absolute top-4 right-4 px-2 py-1 text-xs font-bold text-white rounded-full ${device.isonline ? "bg-green-600" : "bg-red-600"}`}
        >
          {device.isonline ? "ONLINE" : "OFFLINE"}
        </div>
      </div>
      <div className="p-6">
        <h2 className="mb-2 text-xl font-bold tracking-tight text-foreground">
          {device.modelname}
        </h2>
        <p className="mb-1 text-sm text-muted-foreground">{device.ap}</p>
        <p className="mb-4 text-xs text-muted-foreground">
          Agent Version: {device.agentVersion}
        </p>
        <div className="space-y-3">
          <InfoRow label="MAC Address" value={device.mac} />
          <InfoRow label="IP Address" value={device.ipaddress} />
          <InfoRow label="Hostname" value={device.hostname} />
        </div>
        <div className="pt-6 mt-6 border-t border-border">
          <button
            onClick={() => setIsModalOpen(true)}
            className="w-full inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors h-10 px-4 py-2 bg-primary text-primary-foreground hover:bg-primary/90 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          >
            View Details
          </button>
        </div>
      </div>
    </div>
  );
};

export default DeviceCard;
