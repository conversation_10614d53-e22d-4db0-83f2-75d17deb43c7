import { useAppStore } from "@/stores/app-store";
import { memo } from "react";
import { DIALOG_COMPONENTS } from "./dialog-mapping";

const Dialog = memo(({ dialog }) => {
  const { closeDialogs } = useAppStore();
  const { id, data } = dialog;

  const handleClose = () => {
    closeDialogs(id);
  };

  const DialogComponent = DIALOG_COMPONENTS[id];

  if (!DialogComponent) {
    console.error(`Dialog component not found for id: ${id}`);
    return null;
  }

  return <DialogComponent data={data} onClose={handleClose} />;
});

const Dialogs = () => {
  const { dialogs } = useAppStore();

  if (!dialogs?.length) return null;

  return dialogs.map((dialog) => <Dialog key={dialog.id} dialog={dialog} />);
};

export default memo(Dialogs);
