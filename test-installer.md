## Test v1.0.11-prerelease report

- Tested on 2025/08/13 with bbrootsvc version 1.0.11-prerelease

### User guide section 2

- run get-machine-id.exe : **pass**
- generating license key file using https://nimbl.blackbeartechhive.com/nimblkey : **pass**
- show version of license key file : **pass**
  -./ bbrootsvc -version -license C:\workspace\project\bbtechhive\nmskey
- run bbrootsvc without license file : **pass**
  - ./bbrootsvc -n root
    expected output :- license error
    Actual output :- license error
- run bbrootsvc with valid license file(nmskey) same directory : **pass**
  - ./bbrootsvc -n root or ./bbrootsvc -n root -license nmskey
- run bbrootsvc with different Machine ID license file(nmskey) : **pass**
  - ./bbrootsvc -n root -license C:\workspace\project\bbtechhive\nmskey
    expected output :- license error with message "Machine ID is diffrent from license machine id"
    Actual output :- license error with message "Machine ID is diffrent from license machine id"
- run bbrootsvc with valid license file(nmskey) in different directory : **pass**
  - ./bbrootsvc -n root -license C:\workspace\project\bbtechhive\nmskey
- run bbrootsvc with bbrootsvc and altered/edited license file(nmskey) : **pass**
  - ./bbrootsvc -n root -license C:\workspace\project\bbtechhive\nmskey // nmskey is edited
    expected output :- license error
    Actual output :- license error
- run bbrootsvc with No Feature enabled(IDPS not enabled) license file(nmskey) : **pass**
  - ./bbrootsvc -n root -license C:\workspace\project\bbtechhive\nmskey // nmskey is generated with IDPS not enabled
    expected output :- NIMBL UI side navbar should not show IDPS menu
    Actual output :- NIMBL UI side navbar does not show IDPS menu
