import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>alog,
  <PERSON>alogClose,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useValidateSecret } from "@/services/mutations";
import { useAuthStore } from "@/stores/auth-store";
import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "@tanstack/react-router";
import React, { useRef } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

const DEFAULT_REDIRECT = "/dashboard";

const Validate2Fa = ({ data, onClose }) => {
  const formRef = useRef(null);
  const validateSecret = useValidateSecret();
  const navigate = useNavigate();
  const { setAuthData } = useAuthStore();

  const faform = useForm({
    resolver: zodResolver(
      z.object({
        code: z.string().min(1, "Code is required"),
      })
    ),
    defaultValues: {
      code: "",
    },
  });

  const handleValidateCode = async (values) => {
    if (!data?.sessionID) {
      return;
    }
    try {
      const resdata = await validateSecret.mutateAsync({
        sessionID: data.sessionID,
        code: values.code,
      });
      await setAuthData(resdata);
      navigate({ to: DEFAULT_REDIRECT });
    } catch (error) {
      console.log(error);
    } finally {
      onClose();
    }
  };

  const handleClose = () => {
    faform.reset();
    onClose();
  };

  return (
    <Dialog open onOpenChange={handleClose}>
      <Form {...faform}>
        <form
          ref={formRef}
          onSubmit={faform.handleSubmit(handleValidateCode)}
          autoComplete="off"
        >
          <DialogContent
            forceMount={true}
            className="sm:max-w-md"
            onEscapeKeyDown={(e) => e.preventDefault()}
            onPointerDownOutside={(e) => e.preventDefault()}
            onInteractOutside={(e) => e.preventDefault()}
          >
            <DialogHeader>
              <DialogTitle>Validate Code</DialogTitle>
              <DialogDescription>
                Open Google Authenticator to get 2fa code
              </DialogDescription>
            </DialogHeader>
            <div className="flex items-center gap-2">
              <div className="grid flex-1 gap-2">
                <FormField
                  control={faform.control}
                  name="code"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>2FA Code</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter your 2FA code" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
            <DialogFooter className="sm:justify-end">
              <DialogClose asChild>
                <Button type="button" variant="secondary" onClick={handleClose}>
                  Close
                </Button>
              </DialogClose>
              <Button
                variant="default"
                className="ml-2"
                onClick={() => {
                  formRef.current?.dispatchEvent(
                    new Event("submit", { bubbles: true })
                  );
                }}
              >
                Validate
              </Button>
            </DialogFooter>
          </DialogContent>
        </form>
      </Form>
    </Dialog>
  );
};

export default Validate2Fa;
