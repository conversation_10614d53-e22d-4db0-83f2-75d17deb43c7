import { useGetDevices } from "@/services/queries";
import React from "react";
import DeviceCard from "./DeviceCard";

const DevicesComponent = () => {
  const { data: deviceData = [], isLoading, error } = useGetDevices("device");
  console.log(deviceData);
  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 w-full max-w-7xl mx-auto">
      {deviceData.map((item) => (
        <DeviceCard key={item.mac} device={item} />
      ))}
    </div>
  );
};

export default DevicesComponent;
