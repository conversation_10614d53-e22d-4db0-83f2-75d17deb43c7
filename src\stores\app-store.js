import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

export const useAppStore = create(
  devtools(
    immer((set) => ({
      dialogs: [],
      openDialogs: (dialog) =>
        set((state) => {
          state.dialogs.push(dialog);
        }),
      closeDialogs: (id) =>
        set((state) => {
          state.dialogs = state.dialogs.filter((d) => d.id !== id);
        }),
    }))
  )
);
