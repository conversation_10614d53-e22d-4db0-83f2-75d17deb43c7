import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useForm } from "react-hook-form";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useNavigate } from "@tanstack/react-router";
import { useLogin } from "@/services/mutations";
import { useState } from "react";
import { useAuthStore } from "@/stores/auth-store";
import { toast } from "sonner";
import { Loader2Icon } from "lucide-react";
import { useAppStore } from "@/stores/app-store";

const LOCK_DURATION = 10000; // 10 seconds
const DEFAULT_REDIRECT = "/dashboard";

export function LoginForm({ className, ...props }) {
  const [isLocked, setIsLocked] = useState(false);
  const navigate = useNavigate();
  const loginMutation = useLogin();
  const auth = useAuthStore();
  const { openDialogs } = useAppStore();

  const formSchema = z.object({
    user: z.string().min(1, "Username is required"),
    password: z.string().min(1, "Password is required"),
  });

  const userForm = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      user: "",
      password: "",
    },
  });

  const handleOnSubmit = async (values) => {
    console.log("Form submitted with data:", values);
    // Handle login logic here
    const { user, password } = values;

    try {
      const authData = await loginMutation.mutateAsync({ user, password });
      if (authData.sessionID) {
        openDialogs({ id: "validate2fa", data: authData });
      } else {
        await auth.setAuthData(authData);
        await navigate({ to: DEFAULT_REDIRECT });
      }
    } catch (error) {
      handleLoginError(error);
    } finally {
      userForm.reset();
    }
  };

  const handleLoginError = (error) => {
    console.log(error);
    const errorMessage = getErrorMessage(error);
    showErrorNotification(errorMessage);
    lockLoginTemporarily();
  };

  const getErrorMessage = (error) => {
    if (error.response?.data) return error.response.data;
    if (error.message) return error.message;
    return "Something went wrong!";
  };

  const showErrorNotification = (message) => {
    toast.error(message, {
      duration: 5000,
      position: "top-right",
    });
    console.error("Login error:", message);
  };

  const lockLoginTemporarily = () => {
    setIsLocked(true);
    setTimeout(() => setIsLocked(false), LOCK_DURATION);
  };

  return (
    <div className={cn("flex flex-col gap-6", className)} {...props}>
      <Card>
        <CardHeader>
          <CardTitle>Login to your account</CardTitle>
          <CardDescription>
            Enter your username & password below to login
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...userForm}>
            <form
              onSubmit={userForm.handleSubmit(handleOnSubmit)}
              autoComplete="off"
            >
              <div className="flex flex-col gap-6">
                <div className="grid gap-3">
                  <FormField
                    control={userForm.control}
                    name="user"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Username</FormLabel>
                        <FormControl>
                          <Input placeholder="Username" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <div className="grid gap-3">
                  <FormField
                    control={userForm.control}
                    name="password"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Password</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Password"
                            type="password"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                <Button type="submit" className="w-full" disabled={isLocked}>
                  {loginMutation.isPending && (
                    <Loader2Icon className="animate-spin" />
                  )}
                  Login
                </Button>
              </div>
            </form>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
}
