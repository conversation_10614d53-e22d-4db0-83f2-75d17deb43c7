import React from "react";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";

const DetailItem = ({ label, value }) => (
  <div className="py-3 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-0">
    <dt className="text-sm font-medium leading-6 text-muted-foreground">
      {label}
    </dt>
    <dd className="mt-1 text-sm leading-6 text-foreground sm:col-span-2 sm:mt-0">
      {value}
    </dd>
  </div>
);

const DeviceDetailsDialog = ({ data, onClose }) => {
  const device = data;
  return (
    <Dialog open onOpenChange={onClose}>
      <DialogContent
        className="w-full max-w-4xl max-h-[90vh]"
        onEscapeKeyDown={(e) => e.preventDefault()}
        onPointerDownOutside={(e) => e.preventDefault()}
        onInteractOutside={(e) => e.preventDefault()}
      >
        <DialogHeader>
          <DialogTitle>{device.modelname}</DialogTitle>
          <DialogDescription>{device.mac}</DialogDescription>
        </DialogHeader>
        <ScrollArea className="h-[400px]">
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
            <div>
              <h4 className="text-md font-semibold text-primary mb-2">
                Network Info
              </h4>
              <DetailItem label="IP Address" value={device.ipaddress} />
              <DetailItem label="Hostname" value={device.hostname} />
              <DetailItem label="Netmask" value={device.netmask} />
              <DetailItem label="Gateway" value={device.gateway} />
              <DetailItem
                label="DHCP Enabled"
                value={device.isdhcp ? "Yes" : "No"}
              />
            </div>
            <div>
              <h4 className="text-md font-semibold text-primary mb-2">
                System Info
              </h4>
              <DetailItem label="Kernel Version" value={device.kernel} />
              <DetailItem label="AP Version" value={device.ap} />
              <DetailItem label="Agent Version" value={device.agentVersion} />
              <DetailItem label="Scanned By" value={device.scannedby} />
              <DetailItem
                label="Online Status"
                value={
                  device.isonline ? (
                    <span className="text-green-400">Online</span>
                  ) : (
                    <span className="text-red-400">Offline</span>
                  )
                }
              />
            </div>

            <div className="md:col-span-2">
              <h4 className="text-md font-semibold text-primary mb-2 pt-4 border-t border-border">
                Core Capabilities
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8">
                <DetailItem
                  label="Agent"
                  value={device.capabilities.agent ? "Enabled" : "Disabled"}
                />
                <DetailItem
                  label="GWD"
                  value={device.capabilities.gwd ? "Enabled" : "Disabled"}
                />
              </div>
            </div>

            <div className="md:col-span-2">
              <h4 className="text-md font-semibold text-primary mb-2 pt-4 border-t border-border">
                SNMP
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8">
                <DetailItem
                  label="SNMP Supported"
                  value={device.snmpSupported === "1" ? "Yes" : "No"}
                />
                <DetailItem
                  label="SNMP Enabled"
                  value={device.snmpEnabled === "1" ? "Yes" : "No"}
                />
              </div>
            </div>

            <div className="md:col-span-2">
              <h4 className="text-md font-semibold text-primary mb-2 pt-4 border-t border-border">
                Syslog Settings
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8">
                <DetailItem
                  label="Log to Server"
                  value={
                    device.syslogSetting?.logToServer === "1"
                      ? "Enabled"
                      : "Disabled"
                  }
                />
                <DetailItem
                  label="Server IP"
                  value={device.syslogSetting?.serverIp}
                />
                <DetailItem
                  label="Server Port"
                  value={device.syslogSetting?.serverPort}
                />
                <DetailItem
                  label="Log Level"
                  value={device.syslogSetting?.logLevel}
                />
                <DetailItem
                  label="Log to Flash"
                  value={
                    device.syslogSetting?.logToFlash === "1"
                      ? "Enabled"
                      : "Disabled"
                  }
                />
              </div>
            </div>

            <div className="md:col-span-2">
              <h4 className="text-md font-semibold text-primary mb-2 pt-4 border-t border-border">
                SNMP Trap Settings
              </h4>
              <div className="overflow-x-auto">
                <table className="min-w-full text-sm text-left">
                  <thead className="bg-secondary text-secondary-foreground">
                    <tr>
                      <th className="px-4 py-2">Server IP</th>
                      <th className="px-4 py-2">Port</th>
                      <th className="px-4 py-2">Community</th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-border">
                    {/* {device.trapSetting.map((trap, index) => ( */}
                      <tr key={index}>
                        <td className="px-4 py-2">{trap.serverIp}</td>
                        <td className="px-4 py-2">{trap.serverPort}</td>
                        <td className="px-4 py-2">{trap.community}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            <div className="md:col-span-2">
              <h4 className="text-md font-semibold text-primary mb-2 pt-4 border-t border-border">
                Supported Capabilities
              </h4>
              <div className="flex flex-wrap pt-2">
                {device.supported.map((cap) => (
                  <Badge key={cap}>{cap}</Badge>
                ))}
              </div>
            </div>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

export default DeviceDetailsDialog;
