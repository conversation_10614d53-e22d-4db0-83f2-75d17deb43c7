import { Outlet, createRootRoute } from "@tanstack/react-router";
import { TanStackRouterDevtools } from "@tanstack/react-router-devtools";

import TanStackQueryLayout from "../integrations/tanstack-query/layout.tsx";
import { Toaster } from "@/components/ui/sonner.tsx";
import Dialogs from "@/dialogs/dialogs.jsx";

export const Route = createRootRoute({
  component: () => (
    <>
      <Outlet />
      <Dialogs />
      <Toaster richColors duration={5000} position="top-center" />
      <TanStackRouterDevtools />
      <TanStackQueryLayout />
    </>
  ),
});
