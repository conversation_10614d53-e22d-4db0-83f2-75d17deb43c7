import axios from "axios";

const STORAGE_KEYS = {
  SETTINGS: "nimbl-setting",
  AUTH: "nimbl-auth",
};

const DEFAULT_BASE_URL = "http://localhost:27182";
const API_VERSION = "v1";

/**
 * Get base URL from local storage or return default
 * @returns {string} Base URL for API calls
 */
const getBaseURL = () => {
  const settings = localStorage.getItem(STORAGE_KEYS.SETTINGS);
  return settings ? JSON.parse(settings).state.baseURL : DEFAULT_BASE_URL;
};

/**
 * Get auth token from session storage
 * @returns {string} Authentication token
 */
const getAuthToken = () => {
  const auth = sessionStorage.getItem(STORAGE_KEYS.AUTH);
  return auth ? JSON.parse(auth).state.token : "";
};

/**
 * Create axios instance with base configuration
 */
export const axiosInstance = axios.create({
  baseURL: getBaseURL(),
});

/**
 * Create axios instance token interceptor for request configuration
 */
axiosInstance.interceptors.request.use((config) => {
  const token = getAuthToken();
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }
  return config;
});

/**
 * API endpoints
 */
const endpoints = {
  status: "api",
  login: `api/${API_VERSION}/login`,
  devices: `api/${API_VERSION}/devices`,
  topology: `api/${API_VERSION}/topology`,
  commands: `api/${API_VERSION}/commands`,
  portInfo: (mac) => `api/${API_VERSION}/agent/ports?mac=${mac}`,
  allCommands: `api/${API_VERSION}/commands?cmd=all`,
  getCommand: (cmd) => `api/${API_VERSION}/commands?cmd=${cmd}`,
  users: `api/${API_VERSION}/users`,
  secrete: `api/${API_VERSION}/2fa/secret`,
  validate2fa: `api/${API_VERSION}/2fa/validate`,
  syslogs: `api/${API_VERSION}/syslogs`,
  rootInfo: `api/${API_VERSION}/info`, // get root information
  clusterInfo: `api/${API_VERSION}/register`, // get services information
  sshtunnels: `api/${API_VERSION}/ssh/tunnels`,
  importKvStore: `api/${API_VERSION}/kvstore/import`,
  exportKvStore: `api/${API_VERSION}/kvstore/export`,
  addTopology: `api/${API_VERSION}/topology`,
  saveRestoreTopology: `api/${API_VERSION}/topology/action`,
  syslogAlert: `api/${API_VERSION}/syslogs/alert`,
};

/**
 * API error handler
 * @param {Error} error - Axios error object
 * @throws {Error} Enhanced error with context
 */
const handleApiError = (error) => {
  const message = error.response?.data?.message || error.message;
  throw new Error(`API Error: ${message}`);
};

/**
 * Network Management System API methods
 */
export const api = {
  /**
   * Get server status
   * @returns {Promise<Object>} Server status
   */
  getServerStatus: async () => {
    try {
      const { data } = await axiosInstance.get(endpoints.status);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Login to the system
   * @param {Object} credentials - Login credentials
   * @returns {Promise<Object>} Login response
   */
  login: async (credentials) => {
    try {
      const { data } = await axiosInstance.post(endpoints.login, credentials);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get network devices
   * @returns {Promise<Array>} List of devices
   */
  getDevices: async () => {
    try {
      const { data } = await axiosInstance.get(endpoints.devices);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get topology data
   * @returns {Promise<Object>} Network topology
   */
  getTopologyData: async () => {
    try {
      const { data } = await axiosInstance.get(endpoints.topology);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Send command to a device
   * @param {Object} command - Command data
   * @returns {Promise<Object>} Command response
   */
  sendCommand: async (command) => {
    try {
      const { data } = await axiosInstance.post(endpoints.commands, command);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get port information from the agent
   * @returns {Promise<Object>} Port information
   */
  getPortInfo: async (mac) => {
    try {
      const { data } = await axiosInstance.get(endpoints.portInfo(mac));
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get all commands
   * @returns {Promise<Array>} List of all commands
   */
  getAllCommads: async () => {
    try {
      const { data } = await axiosInstance.get(endpoints.allCommands);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get command result
   * @param {string} cmd - Command to get result for
   * @returns {Promise<Object>} Command result
   */
  getCommandResult: async (cmd) => {
    try {
      const { data } = await axiosInstance.get(endpoints.getCommand(cmd));
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get commands with query parameters
   * @param {string} query - Query string (e.g., "last=10")
   * @returns {Promise<Object>} Commands data
   */
  getCommands: async (query) => {
    try {
      const { data } = await axiosInstance.get(
        `${endpoints.commands}?${query}`
      );
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get all users
   * @returns {Promise<Array>} List of users
   */
  getAllUsers: async () => {
    try {
      const { data } = await axiosInstance.get(endpoints.users);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * create a new user
   * @param {Object} user - User data
   * @returns {Promise<Object>} Created user data
   */
  createNewUser: async (user) => {
    try {
      const { data } = await axiosInstance.post(endpoints.users, user);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * edit an existing user
   * @param {Object} user - User data
   * @returns {Promise<Object>} Edited user data
   */
  editUser: async (user) => {
    try {
      const { data } = await axiosInstance.put(endpoints.users, user);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  deleteUser: async (user) => {
    try {
      const { email, name } = user;
      const { data } = await axiosInstance.delete(endpoints.users, {
        data: {
          email,
          name,
        },
      });
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  generateSecret: async (user) => {
    try {
      const { data } = await axiosInstance.post(endpoints.secrete, user);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  deleteSecret: async (userData) => {
    try {
      const { user } = userData;
      const { data } = await axiosInstance.delete(endpoints.secrete, {
        data: { user },
      });
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  validate2fa: async (data2fa) => {
    try {
      const { data } = await axiosInstance.post(endpoints.validate2fa, data2fa);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get syslogs
   * @returns {Promise<Array>} List of syslogs
   */
  getSyslogs: async (params) => {
    try {
      const { number, start, end } = params;
      const { data } = await axiosInstance.get(endpoints.syslogs, {
        params: {
          number,
          start,
          end,
        },
      });
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get root information
   * @returns {Promise<Object>} Root information
   */
  getRootInfo: async () => {
    try {
      const { data } = await axiosInstance.get(endpoints.rootInfo);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get cluster information
   * @returns {Promise<Object>} Cluster information
   */
  getClusterInfo: async () => {
    try {
      const { data } = await axiosInstance.get(endpoints.clusterInfo);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get ssh tunnels
   * @returns {Promise<Object>} SSH tunnels
   */
  getSSHTunnels: async () => {
    try {
      const { data } = await axiosInstance.get(endpoints.sshtunnels);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Import KV Store data
   * @param {Object} kvData - Key-value pairs to import
   * @returns {Promise<Object>} Import response
   */
  importKvStore: async (kvData) => {
    try {
      const { data } = await axiosInstance.post(
        endpoints.importKvStore,
        kvData
      );
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Export KV Store data
   * @returns {Promise<Object>} Key-value pairs from the store
   */
  exportKvStore: async () => {
    try {
      const { data } = await axiosInstance.get(endpoints.exportKvStore);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Add manual topology
   * @param {Object} topoData - Topology data to add
   * @returns {Promise<Object>} Topology data
   */
  addTopology: async (topoData) => {
    try {
      const { data } = await axiosInstance.post(
        endpoints.addTopology,
        topoData
      );
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Save/Restore topology
   * @param {Object} topoActionData - Topology action data
   * @returns {Promise<Object>} Topology data
   */
  saveRestoreTopology: async (topoActionData) => {
    try {
      const { data } = await axiosInstance.post(
        endpoints.saveRestoreTopology,
        topoActionData
      );
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },

  /**
   * Get syslog alert
   * @returns {Promise<Object>} Syslog alert
   */
  getSyslogAlert: async () => {
    try {
      const { data } = await axiosInstance.get(endpoints.syslogAlert);
      return data;
    } catch (error) {
      handleApiError(error);
    }
  },
};
